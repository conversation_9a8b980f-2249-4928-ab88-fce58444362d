<style>
    .reportsTabs {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        row-gap: 4px;
        gap: 4px;
        padding: 8px;
    }

    .reportsTabs .tab {
        display: flex;
        flex: 0 0 calc(16.66% - 20px);
        justify-content: center;
        border-radius: 3px;
        padding: 4px;
        margin: 0;
        cursor: pointer;
    }
</style>
{var $tabs = [
    [
        'url' => '/scumfeed?act=all',
        'title' => 'Все',
        'translate' => false,
        'active' => $mode === 'all'
    ],
    [
        'url' => '/scumfeed?act=post',
        'title' => 'Записи',
        'translate' => false,
        'active' => $mode === 'post'
    ],
    [
        'url' => '/scumfeed?act=photo',
        'title' => 'Фотографии',
        'translate' => false,
        'active' => $mode === 'photo'
    ],
    [
        'url' => '/scumfeed?act=video',
        'title' => 'Видеозаписи',
        'translate' => false,
        'active' => $mode === 'video'
    ],
    [
        'url' => '/scumfeed?act=group',
        'title' => 'Сообщества',
        'translate' => false,
        'active' => $mode === 'group'
    ],
    [
        'url' => '/scumfeed?act=comment',
        'title' => 'Комментарии',
        'translate' => false,
        'active' => $mode === 'comment'
    ],
    [
        'url' => '/scumfeed?act=note',
        'title' => 'Заметки',
        'translate' => false,
        'active' => $mode === 'note'
    ],
    [
        'url' => '/scumfeed?act=app',
        'title' => 'Приложения',
        'translate' => false,
        'active' => $mode === 'app'
    ],
    [
        'url' => '/scumfeed?act=user',
        'title' => 'Пользователи',
        'translate' => false,
        'active' => $mode === 'user'
    ],
    [
        'url' => '/scumfeed?act=audio',
        'title' => 'audios',
        'active' => $mode === 'audio'
    ],
    [
        'url' => '/scumfeed?act=docs',
        'title' => 'documents',
        'active' => $mode === 'docs'
    ]
]}
{include "../components/ui_rmenu.xml", items => $tabs}

<script>
    async function getReports(mode) {
        let _content = $(".page_block.list_view").length;
        $(".page_block.list_view").empty();

        await $.ajax({
            type: "POST",
            url: `/scumfeed?act=${ mode}`,
            data: {
                hash: {=$csrfToken}
            },
            success: (response) => {
                if (response?.reports?.length != _content) {
                    NewNotification("Обратите внимание", "В списке появились новые жалобы. Работа ждёт :)");
                }

                if (response.reports.length > 0) {
                    response.reports.forEach((report) => {
                        $(".page_block.list_view").append(`
                            <div class="content">
                                <table>
                                    <tbody>
                                        <tr>
                                            <td valign="top">
                                                <a href="/admin/report${ report.id}">
                                                    <center>
                                                        <img src="/assets/packages/static/openvk/img/note_icon.png" style="margin-top: 17px;">
                                                    </center>
                                                </a>
                                            </td>
                                            <td valign="top" style="width: 100%">
                                                <a href="/admin/report${ report.id}">
                                                    <b>
                                                        Жалоба №${ report.id}
                                                    </b>
                                                </a>
                                                <br>
                                                <a href="${ report.author.url}">
                                                    ${ report.author.name}
                                                </a>
                                                пожаловал${ report.author.is_female ? "ась" : "ся"} на
                                                ${ report.content.type === "user" ? `<a href="${ report.content.url}">` : ''}
                                                    ${ report.content.name}
                                                ${ report.content.type === "user" ? '</a>' : ''}

                                                ${ report.duplicates > 0 ? `
                                                    <br />
                                                    <b>Другие жалобы на этот контент: <a href="/scumfeed?orig=${ report.id}">${ report.duplicates} шт.</a></b>
                                                ` : ''}
                                            </td>
                                            <td valign="top" class="action_links" style="width: 150px;">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        `);
                    });
                } else {
                    $(".page_block.list_view table").width("100%")
                    $(".page_block.list_view").html(`
                        <center style="background: white;border: #DEDEDE solid 1px;">
                            <span style="color: #707070;margin: 60px 0;display: block;">
                                {_no_data_description|noescape}
                            </span>
                        </center>
                    `);
                }
            }
        });
    }

    $(".reportsTabs .tab").on("click", async function () {
        let mode = $(this).attr("mode");

        $(".reportsTabs #activetabs").attr("id", "ki");
        $(".reportsTabs #act_tab_a").attr("id", "ki");
        $(`.reportsTabs .tab[mode='${ mode}']`).attr("id", "activetabs");
        $(`.reportsTabs .tab[mode='${ mode}'] a`).attr("id", "act_tab_a");

        $(".container_gray").hide();
        $("#reports-loader").show();

        history.pushState(null, null, `/scumfeed?act=${ mode}`);

        await getReports(mode);

        $(".container_gray").show();
        $("#reports-loader").hide();
    });

    setInterval(async () => {
        await getReports($(".reportsTabs #activetabs").attr("mode"));
    }, 10000);
</script>