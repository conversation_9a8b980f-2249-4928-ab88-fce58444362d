/**
 * Reports Page Patch for VKify Theme
 * 
 * Modernizes the reports/scumfeed functionality to use:
 * - Umbrella JS instead of jQuery
 * - Router patch functionality for smooth navigation
 * - Modern fetch API for AJAX requests
 * - Proper integration with ui_rmenu component
 */

window.reportsManager = {
    currentMode: null,
    refreshInterval: null,
    isLoading: false,

    init() {
        if (!window.location.pathname.includes('/scumfeed')) return;
        
        this.currentMode = this.getModeFromUrl();
        this.bindEvents();
        this.startAutoRefresh();
    },

    getModeFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('act') || 'all';
    },

    bindEvents() {
        u(document).on('click', '.ui_rmenu_item', (e) => {
            const link = e.target.closest('a');
            if (!link || !link.href.includes('/scumfeed')) return;

            e.preventDefault();
            const url = new URL(link.href);
            const mode = url.searchParams.get('act') || 'all';
            this.switchMode(mode);
        });
    },

    async switchMode(mode) {
        if (this.isLoading || mode === this.currentMode) return;
        
        this.currentMode = mode;
        this.updateActiveTab(mode);
        this.showLoading();
        
        try {
            await this.loadReports(mode);
            if (window.router && window.router.route) {
                window.router.route(`/scumfeed?act=${mode}`);
            } else {
                history.pushState(null, null, `/scumfeed?act=${mode}`);
            }
        } catch (error) {
            console.error('Failed to load reports:', error);
            this.showError();
        } finally {
            this.hideLoading();
        }
    },

    updateActiveTab(mode) {
        u('.ui_rmenu_item').removeClass('ui_rmenu_item_sel');
        u(`.ui_rmenu_item[href*="act=${mode}"]`).addClass('ui_rmenu_item_sel');
        u(`.ui_rmenu_item[href="/scumfeed"]`).toggleClass('ui_rmenu_item_sel', mode === 'all');
    },

    showLoading() {
        this.isLoading = true;
        const listView = u('.page_block.list_view');
        if (listView.length) {
            listView.html('<div class="pr"><div class="pr_bt"></div><div class="pr_bt"></div><div class="pr_bt"></div></div>');
        }
    },

    hideLoading() {
        this.isLoading = false;
    },

    async loadReports(mode) {
        const formData = new FormData();
        const csrfToken = window.router?.csrf ||
                         u('input[name="hash"]').first()?.value ||
                         u('meta[name="csrf-token"]').attr('content');

        if (csrfToken) {
            formData.append('hash', csrfToken);
        }

        const response = await fetch(`/scumfeed?act=${mode}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-OpenVK-Ajax-Query': '1'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Invalid response format');
        }

        const data = await response.json();
        this.renderReports(data.reports || []);

        if (data.reports && data.reports.length > 0) {
            this.checkForNewReports(data.reports.length);
        }
    },

    renderReports(reports) {
        const listView = u('.page_block.list_view');
        if (!listView.length) return;

        if (reports.length === 0) {
            listView.html(`
                <center style="background: white; border: #DEDEDE solid 1px;">
                    <span style="color: #707070; margin: 60px 0; display: block;">
                        Нет данных для отображения
                    </span>
                </center>
            `);
            return;
        }

        const reportsHtml = reports.map(report => this.renderReport(report)).join('');
        listView.html(reportsHtml);
    },

    renderReport(report) {
        const duplicatesHtml = report.duplicates > 0 ? `
            <br>
            <b>Другие жалобы на этот контент: <a href="/scumfeed?orig=${report.id}">${report.duplicates} шт.</a></b>
        ` : '';

        const contentLink = report.content.type === "user" ? 
            `<a href="${report.content.url}">${report.content.name}</a>` : 
            report.content.name;

        return `
            <div class="content">
                <table>
                    <tbody>
                        <tr>
                            <td valign="top">
                                <a href="/admin/report${report.id}">
                                    <center>
                                        <img src="/assets/packages/static/openvk/img/note_icon.png" style="margin-top: 17px;">
                                    </center>
                                </a>
                            </td>
                            <td valign="top" style="width: 100%">
                                <a href="/admin/report${report.id}">
                                    <b>Жалоба №${report.id}</b>
                                </a>
                                <br>
                                <a href="${report.author.url}">${report.author.name}</a>
                                пожаловал${report.author.is_female ? "ась" : "ся"} на
                                ${contentLink}
                                ${duplicatesHtml}
                            </td>
                            <td valign="top" class="action_links" style="width: 150px;">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
    },

    checkForNewReports(currentCount) {
        if (this.lastReportCount && currentCount > this.lastReportCount) {
            if (window.NewNotification) {
                NewNotification("Обратите внимание", "В списке появились новые жалобы. Работа ждёт :)");
            }
        }
        this.lastReportCount = currentCount;
    },

    showError() {
        const listView = u('.page_block.list_view');
        if (listView.length) {
            listView.html(`
                <center style="background: white; border: #DEDEDE solid 1px;">
                    <span style="color: #707070; margin: 60px 0; display: block;">
                        Ошибка загрузки данных. Попробуйте обновить страницу.
                    </span>
                </center>
            `);
        }
    },

    startAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        this.refreshInterval = setInterval(() => {
            if (!this.isLoading && this.currentMode) {
                this.loadReports(this.currentMode).catch(console.error);
            }
        }, 10000);
    },

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    },

    destroy() {
        this.stopAutoRefresh();
        u(document).off('click', '.ui_rmenu_item');
    }
};

document.addEventListener('DOMContentLoaded', () => {
    window.reportsManager.init();
});

if (window.router && window.router.addEventListener) {
    window.router.addEventListener('route', () => {
        setTimeout(() => window.reportsManager.init(), 100);
    });
} else {
    document.addEventListener('page:loaded', () => {
        setTimeout(() => window.reportsManager.init(), 100);
    });
}

window.addEventListener('beforeunload', () => {
    window.reportsManager.destroy();
});
