@font-face {
    font-family: '<PERSON><PERSON>';
    font-style: normal;
    font-stretch: 100%;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v47/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBGEe.woff2) format('woff2');
}

@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-stretch: 100%;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v47/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBA.woff2) format('woff2');
}

:root {
    --font-family: -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Geneva, Noto Sans Armenian, Noto Sans Bengali, Noto Sans Cherokee, Noto Sans Devanagari, Noto Sans Ethiopic, Noto Sans Georgian, Noto Sans Hebrew, Noto Sans Kannada, Noto Sans Khmer, <PERSON>o Sans Lao, <PERSON><PERSON> Sans Osmanya, <PERSON><PERSON> Sans Tamil, Noto Sans Telugu, Noto Sans Thai, sans-serif
}

body {
    --layout-width: 960px;
    --page-width: 795px;
    --header-height: 42px;
    --module-border-radius: 2px;
    --button-border-radius: 2px;
    --tooltip-border-radius: 3px;

    /* Base colors */
    --white: #fff;
    --black: #000;
    --transparent: transparent;

    /* Background colors */
    --body-background-color: #edeef0;
    --module-background-color: var(--white);
    --module-background-color--secondary: #f0f2f5;
    --module-header-background-color: #fafbfc;
    --header-background-color: #507299;
    --header-background-color--hover: #486991;
    --header-background-color--active: #43648c;
    --button-background-color: #e5ebf1;
    --button-background-color--hover: #dfe6ed;
    --button-background-color--light: #f2f4f7;
    --dimmer-background-color: var(--black);

    /* Border and shadow colors */
    --shadow-outline-color: #e3e4e8;
    --shadow-bottom-color: #d7d8db;
    --border-color: #e7e8ec;
    --border-color-2: #c5d0db;
    --border-color-3: #d3d9de;
    --border-color-4: #cad2db;

    /* Text colors */
    --text-color: var(--black);
    --header-text-color: var(--white);
    --muted-text-color: #939393;
    --muted-text-color-2: #656565;
    --muted-text-color-3: #828a99;
    --heading-color: #222;
    --link-color: #2a5885;
    --link-color-2: #55677d;

    /* Sidebar colors */
    --sidebar-color--hover: #e1e5eb;
    --sidebar-text-color: #285473;
    --sidebar-count-color: #d1d9e0;
    --sidebar-count-color--hover: #bbc7d3;
    --sidebar-count-text-color: #5b6e85;

    /* Search colors */
    --search-color: #31537a;
    --search-color--active: var(--white);
    --search-text-color: #d9e2ec;
    --search-text-color--active: var(--text-link);
    --search-text-color--placeholder: #8fadc8;

    /* Accent colors */
    --accent-color: #5e81a8;
    --accent-color--hover: #6888ad;
    --accent-color--rgb: 94, 129, 168;
    --accent-text-color: var(--white);
    --accent-color-2: #5181b8;
    --accent-color-3: #6287ae;
    --accent-color-4: #577ea2;
    --accent-color-5: #5185be;
    --accent-color-6: #638ab1;

    /* Success colors */
    --success-color: #5fb053;
    --success-color--hover: #68b35d;
    --success-text-color: var(--white);

    /* Audio player colors */
    --audio-background-color: #edeff1;
    --audio-background-color-2: #f5f7fa;
    --audio-background-color-3: #e8edf4;
    --audio-slider-color: #e4e7ea;
    --audio-slider-color-2: #e1e8ee;
    --audio-slider-progress-color: #b8c7d7;
    --audio-count-color: #cfd9e0;
    --audio-count-text-color: #5e6a79;

    /* Tooltip colors */
    --tooltip-background-color: rgba(0, 0, 0, 0.7);
    --tooltip-background-color-2: rgba(194, 206, 218, .9);
    --tooltip-text-color: #fff;
    --tooltip-text-color-2: #4d637c;

    /* Additional UI colors */
    --menu-divider-color: #dfe2e8;
    --tab-border-color: #e3e7ef;
    --tab-text-color: #828282;
    --dropdown-border-color: #c9d0d6;
    --dropdown-hover-color: #e7edf2;
    --post-action-color: #42648b;
    --post-action-active-color: #6983a0;
    --post-meta-color: #7996b5;
    --quote-border-color: #dee6ee;
    --search-placeholder-color: #929eb0;
    --search-placeholder-focus-color: #c0c8d0;
    --message-warning-background: #ffefe9;
    --message-warning-border: #f2ab99;
    --overlay-background: rgba(0, 0, 0, 0.4);

    /* Profile page colors */
    --album-background-color: #ecf1f5;
    --album-title-muted-color: #8997a5;
    --profile-border-color: #dadbde;
    --profile-button-border-color: #7496b9;
    --video-overlay-background: rgba(0, 0, 0, .5);
    --video-overlay-background-hover: rgba(0, 0, 0, .6);
}

#ajax_audio_player {
    display: none;
}

#backdrop {
    top: var(--header-height);
}

#backdropDripper {
    width: var(--layout-width, 960px);
    background-color: var(--body-background-color);
    box-shadow: -30px 0px 20px 20px var(--body-background-color), -50px 0px 20px 20px hsl(0deg 0% 93% / 59%), -70px 0px 20px 20px hsl(0deg 0% 93% / 43%), -90px 0px 20px 20px hsl(0deg 0% 93% / 35%), -110px 0px 20px 20px hsl(0deg 0% 93% / 28%), -130px 0px 20px 20px hsl(0deg 0% 93% / 16%), 30px 0px 20px 20px var(--body-background-color), 50px 0px 20px 20px hsl(0deg 0% 93% / 59%), 70px 0px 20px 20px hsl(0deg 0% 93% / 43%), 90px 0px 20px 20px hsl(0deg 0% 93% / 35%), 110px 0px 20px 20px hsl(0deg 0% 93% / 28%), 130px 0px 20px 20px hsl(0deg 0% 93% / 16%)
}

body {
    background: var(--body-background-color);
    color: var(--text-color);
    margin: 0;
    padding: 0;
    font-size: 13px;
    font-family: var(--font-family);
    line-height: 1.154;
    font-weight: 400;
    -webkit-font-smoothing: subpixel-antialiased;
    -moz-osx-font-smoothing: auto
}
.theme-switching * {
    transition: .5s color, .5s border, .5s background-color, .5s box-shadow;
}

.page_header {
    position: fixed;
    height: var(--header-height);
    width: 100%;
    background: var(--header-background-color);
    z-index: 100;
    display: flex;
    left: 0;
    justify-content: center;
    top: 0;
}

.page_header_inner {
    width: var(--layout-width, 960px);
    height: 100%;
}

.home_button {
    width: 165px;
    height: var(--header-height);
    position: static;
    display: block;
    float: left;
}

.home_button div {
    background: url("/themepack/vkify16/*******/resource/icons/head_icons.png") no-repeat;
    background-position: 0 -98px;
    height: 19px;
    width: 32px;
    margin: 11px 10px 0 7px;
}

.page_header a:hover, a.button:hover, .mb_tab>a:hover, .tab a:hover, .profile_link:hover {
    text-decoration: none;
}

.layout {
    width: var(--layout-width)
}

.page_body, .messenger-app, .crp-list {
    width: var(--page-width)
}
.page_content {
    width: 100%;
}

.no-sidebar .page_body, .no-sidebar .page_content {
    width: 100%;
}

.page_body {
    font-size: 13px;
    margin-top: calc(var(--header-height) + 15px);
    margin-right: 0;
    padding: 0;
    background-color: var(--transparent);
    box-shadow: none;
    float: left;
}

.wide_column {
    position: relative;
    width: 100%
}

.narrow_column_wrap {
    position: relative;
    width: 230px
}

.narrow_column {
    width: 230px
}

.narrow_column.fixed {
    z-index: 3
}

body.mac .narrow_column.fixed {
    transform: translateZ(0)
}

.wide_column_left .wide_column_wrap {
    margin-right: 245px
}

.wide_column_left .wide_column {
    float: left
}

.wide_column_left .narrow_column_wrap {
    float: right
}

.wide_column_right .wide_column_wrap {
    margin-left: 245px
}

.wide_column_right .wide_column {
    float: right
}

.wide_column_right .narrow_column_wrap {
    float: left
}


.label, .nobold, nobold, .post_header_info .explain, .post_author {
    color: var(--muted-text-color);
}

#auth {
    padding: 0
}

.page_info_wrap {
    padding: 15px 20px 20px;
}

#auth.page_block {
    padding: 37px 30px;
}

.module+.module {
    margin-top: 0;
    border-top: 1px solid var(--border-color);
}

.module_header {
    display: block;
}

.module_header .header_top {
    height: 32px;
    line-height: 40px;
    overflow: hidden;
    padding: 0 15px;
    margin: 0;
    font-weight: 400;
    -webkit-font-smoothing: subpixel-antialiased;
    -moz-osx-font-smoothing: auto;
    font-size: 12.5px;
    letter-spacing: 0;
    outline: none;
    color: var(--text-color);
}

.module_header .header_count {
    color: var(--muted-text-color);
    padding: 0 6px;
}

.module_body {
    padding: 12px 15px 15px;
    font-size: 12.5px;
}

.wide_column .module_header .header_top {
    padding: 2px 20px 0;
    font-size: 13px;
}
.wide_column .module_body {
    padding: 12px 20px 15px;
}

#wrapH, #wrapHI, .page-wrap, .wrap1, .wrap2 {
    border: 0
}

#auth .container_gray {
    margin-left: -10px;
    margin-bottom: -10px;
    width: 768px
}

.sidebar {
    width: 149px;
    margin: calc(var(--header-height) + 15px) 16px 0 0;
}

input, h4, .edit_link {
    font-family: var(--font-family);
    font-weight: 400;
}

input:focus, textarea:focus {
    outline: 0
}

.error_block {
    text-align: center;
    color: var(--muted-text-color-2);
    padding: 80px 20px;
    line-height: 160%;
}

.langSelect {
	display: flex;
	align-items: center;
	padding: 6px 0;
}

.home_search {
    height: 100%;
    line-height: 16px;
    width: 230px;
    float: left;
}

#search_box select[name="section"], #search_box .search_box_button {
    display: none;
}

.home_navigation, .end_navigation {
    margin-left: 12px;
    display: inline-block;
    height: inherit;
}
#top_notify_btn_div {
    position: relative;
    display: inline-block;
    height: 100%;
    width: 46px;
}
.top_notify_count {
    display: none;
    padding: 1px 4px;
    border: 2px solid var(--header-background-color);
    border-radius: 12px;
    color: #fff;
    font-size: 9px;
    height: 11px;
    line-height: 11px;
    min-width: 5px;
    top: 6px;
    left: 21px;
    background-color: #ff734c;
    text-align: center;
    position: absolute;
    white-space: nowrap;
}
#top_notify_btn.has_notify .top_notify_count {
    display: block
}
.no_notifications, .notifications_error {
    padding: 20px;
    text-align: center;
    color: var(--muted-text-color);
}
.home_navigation .top_nav_btn {
    display: inline-block;
    width: 46px;
    transition: opacity 100ms linear;
}
.home_navigation .top_nav_btn_icon {
    display: block;
    margin: 11px auto;
    background: url(/themepack/vkify16/*******/resource/icons/head_icons.png) no-repeat;
    background-position-x: 0%;
    background-position-y: 0%;
    height: 20px;
    width: 20px;
}
.home_navigation .top_nav_btn:hover, .end_navigation .link:hover, .top_audio_player:hover {
    background-color: var(--header-background-color--hover);
}
.top_audio_player.audio_top_btn_active, [aria-expanded="true"] .top_nav_btn, #userMenuTrigger.shown {
    background-color: var(--header-background-color--active);
}
.top_nav_btn#headerMusicBtn .top_nav_btn_icon {
    background-position: -5px -53px;
}
.top_nav_btn#headerMusicBtn:hover .top_nav_btn_icon {
    background-position: -35px -53px;
}
.top_nav_btn#top_notify_btn .top_nav_btn_icon {
    background-position: -5px -23px
}
.top_nav_btn#top_notify_btn:hover .top_nav_btn_icon {
    background-position: -35px -23px
}
[aria-expanded="true"] .top_nav_btn .top_nav_btn_icon,
.top_nav_btn#top_notify_btn.has_notify .top_nav_btn_icon {
    background-position: -65px -23px
}

.end_navigation {
    float: right;
    line-height: var(--header-height);
    height: var(--header-height);
}
.end_navigation a {
    color: var(--header-text-color);
    padding-bottom: 0;
    padding: 0 10px;
    font-weight: 700;
    height: var(--header-height);
    line-height: var(--header-height);
    display: block;
}
.end_navigation #userMenuTrigger {
    padding-right: 0;
    display: flex;
    align-items: center;
    cursor: pointer;
}
.end_navigation #userMenuName {
    margin-right: 10px;
}
.end_navigation #userMenuAvatar {
    height: 28px;
    width: 28px;
    border-radius: 14px;
    margin: 7px 0px;
}
.end_navigation #userMenuArrow {
    background: url(/themepack/vkify16/*******/resource/icons/head_arrow.png) no-repeat;
    background-position: right 0;
    margin: 1px 8px 0 7px;
    width: 8px;
    height: 4px;
    opacity: 0.45;
}
.shown #userMenuArrow {
    transform: rotate(180deg);
}
.header_navigation .header_divider_stick, .header_navigation .link {
    background: 0 0
}

.top_audio_player {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: var(--search-text-color);
    padding-right: 10px;
    position: relative;
    max-width: 300px;
    line-height: 0;
    cursor: pointer;
    display: none;
    height: 42px;
}
.top_audio_player.top_audio_player_enabled, .top_nav_audio {
    display: inline-block;
    vertical-align: top;
}
.top_audio_player .top_audio_player_btn {
    display: inline;
    float: left;
    padding: 16px 4px 12px;
    cursor: pointer;
}
.top_audio_player .top_audio_player_next {
    margin-right: 7px;
}
.top_audio_player .top_audio_player_prev {
    padding-left: 10px;
}
.top_audio_player .top_audio_player_play {
    padding: 14px 4px;
}
.top_audio_player .top_audio_player_btn>div {
    width: 14px;
    height: 14px;
    background-image: url(/themepack/vkify16/*******/resource/icons/head_icons.png);
    opacity: 0.65;
    -o-transition: opacity 60ms linear;
    transition: opacity 60ms linear;
}
.top_audio_player .top_audio_player_btn:hover>div {
    opacity: 1;
}
.top_audio_player .top_audio_player_prev>div {
    background-position: -32px -80px;
}
.top_audio_player .top_audio_player_play>div {
    background-position: 0 -78px;
}
.top_audio_player.top_audio_player_playing .top_audio_player_play>div {
    background-position: -17px -78px;
}
.top_audio_player .top_audio_player_next>div {
    background-position: -48px -80px;
}
.top_audio_player .top_audio_player_title_wrap {
    margin: 12px 0;
    text-overflow: ellipsis;
    display: inline-block;
    overflow: hidden;
    line-height: 16px;
    color: var(--search-text-color);
}
.top_audio_player .top_audio_player_title {
    max-width: 100%;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 225px;
    transition: opacity 60ms linear
}
[data-theme="musicpopup"].tippy-box {
    background-color: var(--module-background-color);
    border: 1px solid var(--border-color-2);
    border-radius: var(--tooltip-border-radius);
    box-shadow: 0 1px 3px rgba(0, 0, 0, .1);
    border-top-width: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    width: 795px;
    color: inherit;
    font-size: 12.5px;
    max-height: 500px;
}
[data-theme="musicpopup"] .tippy-content {
    overflow: hidden;
    padding: 0;
}
.audio_content_wrap {
    display: flex;
    height: 452px;
}
.audio_content {
    flex: 1;
    display: flex;
    flex-direction: column;
}
.audio_content .audiosSideContainer {
    flex: 1;
    padding: 8px 16px;
    height: 100%;
    overflow-y: auto;
    box-sizing: border-box;
}
.musfooter {
    padding: 15px 14px;
    margin-top: auto;
}
.tippy-box[data-theme="musicpopup"] .wide_column_left {
    display: flex;
}
.tippy-box[data-theme="musicpopup"] .wide_column_left .wide_column_wrap {
    margin: 0;
    width: 100%;
}
.tippy-box[data-theme="musicpopup"] .wide_column_left .wide_column {
    height: 100%;
    display: flex;
    flex-direction: column;
}
.musfooter #ajclosebtn {
    float: right;
}

.bigPlayer {
    background-color: var(--module-header-background-color);
    border-bottom: 1px solid var(--border-color);
    box-shadow: none;
    margin: 0;
}
.bigPlayer .trackInfo a {
    color: var(--text-color);
}
.audiosSideContainer {
    width: 100%;
}
.tippy-box[data-theme="musicpopup"] .audiosSideContainer {
    width: unset;
}
.audiosDiv {
    margin: 0;
    padding: 15px 20px;
    width: unset;
}

.bigPlayer .tip_result {
    width: max-content !important;
    height: 11px !important;
    top: -4px !important;
    border: none !important;
    position: absolute !important;
    z-index: 10 !important;
    transition: all .1s ease-out !important;
    user-select: none !important;
    transform: translate(-12%, -15%) !important;
    color: var(--tooltip-text-color-2) !important;
    background: var(--tooltip-background-color-2) !important;
    padding: 1px 4px 5px 5px !important;
    border-radius: var(--tooltip-border-radius) !important;
    cursor: pointer !important;
}

.bigPlayer .additionalButtons .tip_result, .bigPlayer .playButtons .tip_result {
    transform: translate(-55%, -45%) !important;
}

.bigPlayer .tip_result::after {
    content: "";
    position: absolute;
    bottom: -4px;
    width: 7px;
    height: 4px;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAECAYAAABCxiV9AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAoSURBVBhXYzx07tYzBhyAEURgU2BnpCYFlgQBZAUgCSgTAVBNYGAAAJMuDqQSKxBHAAAAAElFTkSuQmCC') no-repeat scroll transparent;
    margin-left: -45%;
}

#upload_container, #audio_upload {
    background: none;
    padding: 0;
    margin: 0;
    border: none;
}

.page_header #search_box input[type="search"] {
    border: 0;
    box-sizing: border-box;
    height: 28px;
    border-radius: 14px;
    -o-transition: background-color .05s, color .05s;
    transition: background-color .05s, color .05s;
    background: var(--search-color) url(/themepack/vkify16/*******/resource/icons/dev_head_magglass.png) no-repeat;
    background-position: left 8px;
    line-height: 16px;
    border-left: 8px solid transparent;
    color: var(--search-text-color);
    margin: 7px 0;
    padding: 6px 6px 6px 19px;
    font-size: 13px;
    width: 100%;
}
.page_header #search_box input[type="search"]:focus {
    background-color: var(--search-color--active);
    color: var(--search-text-color--active);
}
.page_header.search_expanded #search_and_one_more_wrapper {
    width: 100%;
}
.home_search input[type~=search]::placeholder {
    color: var(--search-text-color--placeholder);
}
.home_search input[type=search]:focus::placeholder {
    color: transparent;
}

.navigation .link {
    display: block;
    padding: 0;
    text-decoration: none;
    border-top: 0;
    color: var(--sidebar-text-color);
    font-size: 12.5px
}
.home_navigation .link {
    display: inline-block;
    height: initial;
    padding: 11px 4px 0 7px;
    background-size: 1.5px 41px
}

.navigation .link:before {
    background: url(/themepack/vkify16/*******/resource/icons/menu_icon.png) no-repeat 7px -441px;
    content: '';
    height: 25px;
    width: 35px;
    opacity: .75;
}
.navigation .link[href="/im"]:before {
    background-position: 7px -21px
}
.navigation .link[accesskey="."]:before {
    background-position: 7px 6px;
}
.navigation .link[href="/feed"]:before {
    background-position: 7px -917px
}
.navigation .link[href^="/friends"]:before {
    background-position: 7px -77px
}
.navigation .link[href^="/albums"]:before {
    background-position: 7px -133px;
}
.navigation .link[href^="/video"]:before {
    background-position: 7px -189px
}
.navigation .link[href^="/audios"]:before {
    background-position: 7px -161px
}
.navigation .link[href="/settings"]::before {
    background: url(/themepack/vkify16/*******/resource/icons/common.png);
    background-position: 10px 175px;
    height: 20px;
}
.navigation .link[href="/apps?act=installed"]:before {
    background-position: 7px -217px;
}
.navigation .link[href^="/notes"]:before {
    background-position: 7px -385px;
}
.navigation .group_link:before, .navigation .link[href^="/groups"]:before {
    background-position: 7px -105px;
}
.navigation .link[href="/donate"]::before {
    background: url(/themepack/vkify16/*******/resource/icons/post_icon.png) no-repeat;
    background-position: 6px -305px;
}
.navigation .link[href="/docs"]:before {
    background-position: 7px -273px
}
.navigation .link[href="/fave"]:before {
    background-position: 7px -301px
}

.left_menu_nav_wrap {
    line-height: 19px;
    font-size: 12px;
    padding: 7px 0px 4px;
}
.left_menu_nav_wrap a {
    padding-right: 10px;
    color: var(--muted-text-color);
    white-space: nowrap;
}
.left_menu_nav_wrap #moreOptionsLink:after {
    display: inline-block;
    content: "";
    background: url(/themepack/vkify16/*******/resource/icons/menu_arrow.png) no-repeat;
    background-position-x: 0%;
    background-position-y: 0%;
    background-position: right 0;
    width: 13px;
    height: 5px;
    opacity: .65;
    margin-right: -10px;
}
.left_menu_nav_wrap #moreOptionsLink:hover::after {
    opacity: 1;
}

.navigation .group_link:before {
    line-height: 0
}

.navigation .link {
    display: flex;
    align-items: center;
    height: 28px;
    line-height: 27px;
}
.navigation .link:hover {
    border-top: 0;
    background-color: var(--sidebar-color--hover);
}

.sidebar .navigation .link object[type="internal/link"] {
    font-size: 0;
    padding: 4px 6px;
    right: 0;
    border-radius: 2px;
    line-height: 17px;
    height: 16px;
    position: absolute;
    margin: 4px 0 4px -6px;
    background-color: var(--sidebar-count-color);
    color: var(--sidebar-count-text-color);
}
.sidebar .navigation .link object[type="internal/link"]:hover {
    background-color: var(--sidebar-count-color--hover);
}
.sidebar .navigation .link object[type="internal/link"] b {
    font-size: 11px;
    color: var(--sidebar-count-text-color);
}

.menu_divider {
    background: var(--menu-divider-color);
    margin-top: 9px;
    margin-bottom: 9px;
    margin-left: 27px;
    margin-right: 6px;
}

#fastLogin {
    padding: 11px 0 0;
}
#fastLogin br {
    display: none;
}
#fastLogin label {
    margin-bottom: 8px;
    color: var(--muted-text-color-2);
    font-size: 12.5px;
    font-weight: 500;
    display: block;
}
#fastLogin input:not(.button) {
    width: 100%;
    margin-bottom: 13px;
}
#fastLogin input.button {
    width: 100%;
    margin: 0;
}
#fastLogin a[href="/reg"] input.button {
    margin-top: 10px;
}
#fastLogin span {
    padding: 0;
}
#fastLogin .forgot, #login_form .forgot {
    padding-top: 16px;
    text-align: center;
}

#login_form {
    margin: 0 auto;
    width: 270px;
}
.login_header {
    margin: 0 0 25px;
    text-align: center;
    font-size: 20px;
}
#login_form input:not(.button, :last-of-type) {
    margin-bottom: 15px;
}
.login_buttons_wrap {
    margin-top: 20px;
    display: flex;
}
.login_buttons_wrap .button_blue {
    margin-right: 10px;
}
#login_form .button {
    width: 100%;
}

.navigation {
    position: sticky;
    top: 20px;
}

.footer_wrap {
    margin: 0;
    line-height: 1.36em;
    padding: 16px 10px 35px;
    text-align: center;
}

.footer_wrap .footer_copy {
    float: left;
}

.footer_wrap .footer_lang {
    float: right;
}

.footer_wrap .footer_lang a {
    margin-left: 5px;
}

.footer_wrap .footer_links a {
    padding: 2px 5px;
}

div.container_gray>form>table {
    width: 45%
}

label:not(:has(a)) {
    display: block;
    margin: 5px 0;
}
label:has(input[type="checkbox"], input[type="radio"]):not(:has(a)) {
    display: flex;
    align-items: center;
}
label:has(input[type="checkbox"], input[type="radio"]):not(:has(a))+br {
    display: none;
}
label:not(:has(a))+label:not(:has(a)) {
    margin-top: 10px;
}
.settings_panel label:has(input[type="checkbox"], input[type="radio"]):not(:has(a)) {
    width: fit-content;
}
.settings_panel label:first-child {
    margin-top: 0;
}

:is(input, select, textarea)+label {
    margin-top: 10px;
}

.display_flex_column:has(label) {
    display: block !important;
}

#showIgnored {
    float: right;
    margin: 2px 2px 0 0;
}

.floating_sidebar.show, .link.edit-button, .page_footer img[src^="/assets/packages/static/openvk/img/flags/"] {
    display: none
}

.content {
    padding: 0
}

.content_list .cl_element .cl_name .cl_lname, .left_small_block br {
    display: none
}

.ava, .crp-entry--message---av>img {
    width: 50px;
    height: 50px
}

.content_list .cl_element .cl_name {
    font-size: 12.5px
}

.content_list.long {
    width: 100%
}

.container_gray .content {
    padding: 0;
    border: 0
}

.page_wrap.padding_top {
    padding-top: 0
}

.page_yellowheader {
    display: none;
}

.bigPlayer .trackPanel {
    width: 110%;
}
.bigPlayer .volumePanel {
    margin-left: 60px;
    padding-top: 17px;
}
.bigPlayer .selectableTrack {
    cursor: pointer;
}
.bigPlayer .selectableTrack {
    overflow: hidden;
    margin: 0;
}
.selectableTrack .selectableTrackLoadProgress {
    top: -2px;
    height: 100%;
    z-index: 0;
}
.selectableTrack .selectableTrackLoadProgress .load_bar {
    height: 100%;
    background: var(--audio-slider-progress-color);
    border-bottom: none;
}
.bigPlayer #bigPlayerLengthSliderWrapper {
    z-index: 1;
}
.bigPlayer .trackPanel .track .selectableTrack>div, .bigPlayer .volumePanel .selectableTrack>div {
    width: 100%;
    font-size: 0;
}
.bigPlayer .selectableTrack:hover {
    margin: -1.5px 0;
}
.bigPlayer .slider {
    position: relative;
    overflow: visible;
    border-radius: 4px;
}
.bigPlayer .slider::before {
    content: "";
    position: absolute;
    top: 0;
    left: -620px;
    width: 620px;
    height: 100%;
    background-color: var(--accent-color-4);
}
.selectableTrack {
    position: relative;
    height: 2px;
    border-top: 0;
    background-color: var(--audio-slider-color);
}
.bigPlayer .slider, .audioEmbed .track .slider {
    width: 3px;
    height: 2px;
    background: var(--accent-color-4);
}
.bigPlayer {
    width: 100%;
}
.bigPlayer .bigPlayerWrapper {
    grid-template-columns: 0fr 1fr 0fr 0fr;
}
.bigPlayer .playButtons {
    width: 100%;
    gap: 4px
}
.bigPlayer .playButtons .arrowsButtons {
    gap: 4px;
}
.bigPlayer .trackPanel {
    width: 100%;
    margin-left: 7px;
}
.bigPlayer .trackPanel .track {
    margin-top: 3px;
}
.bigPlayer .trackInfo .trackName {
    max-width: 255px;
}
.tippy-content .bigPlayer .trackInfo .trackName {
    max-width: 320px;
}
.bigPlayer .timer span:not(.time) {
    display: none;
}
.bigPlayer .volumePanel {
    margin-left: 10px;
    width: 50px;
}
.bigPlayer .trackInfo .timer {
    white-space: nowrap;
}

.audioEmbed .track>.selectableTrack, .bigPlayer .selectableTrack {
    border-top: 0;
    height: 2px;
    background: var(--audio-slider-color-2);
    transition: .2s;
    border-radius: 4px;
    overflow: hidden;
}
.bigPlayer .slider, .audioEmbed .track .slider {
    transition: .2s;
    width: 0px;
}
.audioEmbed .track>.selectableTrack:hover, .bigPlayer .selectableTrack:hover, .bigPlayer .selectableTrack:hover .slider, .audioEmbed .track .selectableTrack:hover .slider, .selectableTrack .selectableTrackLoadProgress .load_bar {
    padding: 1px 0;
}
.bigPlayer .selectableTrack:hover .slider, .audioEmbed .track .selectableTrack:hover .slider {
    top: -1px;
}
.selectableTrack:hover .selectableTrackLoadProgress {
    padding: 1px 0;
    top: -6px;
}

.bigPlayer .volumePanel .selectableTrack>div {
    height: inherit;
}
.bigPlayer .playButtons .playButton {
    width: 28px;
    height: 28px;
}
.bigPlayer .playButtons .playButton, .audioEntry .playerButton .playIcon {
    background-image: none;
    content: url("data:image/svg+xml,%3Csvg fill='none' height='28' viewBox='0 0 28 28' width='28' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath clip-rule='evenodd' d='M28 14a14 14 0 1 1-28 0 14 14 0 0 1 28 0zm-8.98.87c.64-.39.64-1.36 0-1.74l-6.6-4c-.64-.38-1.42.1-1.42.87v8c0 .76.78 1.25 1.41.87z' fill='%235185be' fill-rule='evenodd'%3E%3C/path%3E%3C/svg%3E");
}
.bigPlayer .playButtons .playButton.pause, .audioEntry.nowPlaying .playIcon, .audioEntry .playerButton .playIcon.paused {
    content: url("data:image/svg+xml,%3Csvg fill='none' height='28' viewBox='0 0 28 28' width='28' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath clip-rule='evenodd' d='M28 14a14 14 0 1 1-28 0 14 14 0 0 1 28 0zM10 9.6c0-.33.27-.6.6-.6h1.8c.33 0 .6.27.6.6v8.8a.6.6 0 0 1-.6.6h-1.8a.6.6 0 0 1-.6-.6zm5 0c0-.33.27-.6.6-.6h1.8c.33 0 .6.27.6.6v8.8a.6.6 0 0 1-.6.6h-1.8a.6.6 0 0 1-.6-.6z' fill='%235185be' fill-rule='evenodd'%3E%3C/path%3E%3C/svg%3E")
}
.bigPlayer .playButtons .nextButton {
    margin-top: 3px;
    width: 20px;
    height: 20px;
    background-image: none;
    content: url("data:image/svg+xml,%3Csvg fill='none' height='20' viewBox='0 0 20 20' width='20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.75 15c.41 0 .75-.34.75-.75v-3.18l7.12 3.82a.94.94 0 0 0 1.38-.83V5.94a.94.94 0 0 0-1.38-.83L6.5 8.93V5.75a.75.75 0 0 0-1.5 0v8.5c0 .***********.75z' fill='%23638ab1'%3E%3C/path%3E%3C/svg%3E")
}
.bigPlayer .playButtons .backButton {
    margin-top: 3px;
    width: 20px;
    height: 20px;
    background-image: none;
    content: url("data:image/svg+xml,%3Csvg fill='none' height='20' viewBox='0 0 20 20' width='20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14.25 5a.75.75 0 0 0-.75.75v3.18L6.38 5.1A.94.94 0 0 0 5 5.94v8.12a.94.94 0 0 0 1.38.83l7.12-3.82v3.18a.75.75 0 0 0 1.5 0v-8.5a.75.75 0 0 0-.75-.75z' fill='%23638ab1'%3E%3C/path%3E%3C/svg%3E")
}
.audioEntry .audioEntryWrapper {
    height: auto;
}
.audioEntry .playerButton .playIcon {
    width: 24px;
    height: 24px;
}
.audioEntry .playerButton {
    width: 24px;
    height: 24px;
}
.audioEntry.nowPlaying, .audioEntry.nowPlaying:hover {
    background: var(--audio-background-color);
    outline: 0;
}
.audioEntry.nowPlaying .status, .audioEntry.nowPlaying .performer a, .audioEntry.nowPlaying .title {
    color: unset;
}
.audioEntry.nowPlaying .explicitMark path {
    fill: var(--muted-text-color-3);
}
.audioEntry {
    border-radius: 3px;
    overflow: hidden;
}
.audioEntry:hover {
    background-color: var(--audio-background-color-2);
}
.audioEntry.nowPlaying, .audioEntry.nowPlaying:hover {
    background-color: var(--audio-background-color-3) !important
}
.audioEntry.nowPlaying .performer a, .audioEntry.nowPlaying .performer {
    color: var(--link-color) !important
}
.audioEntry .mini_timer .nobold, .audioEntry.nowPlaying .mini_timer .nobold {
    font-size: 12px;
    color: var(--muted-text-color) !important;
    line-height: 13px;
}
.audiosContainer .loadMore {
    padding: 7px 0;
    display: block;
    text-align: center;
    border-radius: 3px;
}
.audiosContainer .loadMore:hover {
    background-color: var(--audio-background-color-2);
    text-decoration: none;
}

.toTop {
    top: 42px;
}

.common_icon {
    background: url(/themepack/vkify16/*******/resource/icons/common.png) no-repeat;
    width: 14px;
    height: 14px;
}

/* listview */
.list_view {
    padding: 0 20px 18px;
}
.search_row {
    border-top: 1px solid var(--border-color);
    padding: 15px 0;
    clear: both;
    display: flex;
    align-items: center;
    position: relative;
}
.search_row:last-of-type {
    padding-bottom: 0;
}
.search_row:first-child {
    border-top: 0;
}
.search_row .img {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 12px 0 0;
}
.search_row .img img {
    border-radius: 50%;
    background-color: var(--module-background-color--secondary);
    width: 100%;
    height: 100%;
}
.search_row .controls {
    margin: 0 0 0 10px;
    float: right;
    text-align: right;
    width: 155px;
    order: 3;
}
.search_row .controls form+form {
    margin-top: 10px;
}
.search_row .info {
    overflow: hidden;
    order: 2;
    flex: 1;
}
.search_row .labeled.name {
    color: var(--link-color);
    margin-bottom: 5px;
    font-weight: 700;
}
.search_row .labeled, .search_row .labeled:not(.name, .message) a {
    color: var(--muted-text-color-2)
}
.search_row .labeled:not(:last-child) {
    margin-bottom: 6px;
}

center {
    border: 0;
}

center span:only-child, .post-content .text, .post-content, .edit_link, table {
    font-size: 13px;
}

.searchList #used, .verticalGrayTabs #used {
    color: var(--text-color);
    font-weight: 500;
    padding: 7px 5px 7px 18px;
    border: 0;
    border-left: solid 2px var(--accent-color-3);
    background: var(--audio-background-color-2);
}

.searchList li, .searchList a, .verticalGrayTabs a {
    padding: 7px 5px 7px 18px;
}

.page_content {
    position: relative;
}

#__feed_settings_link {
    float: unset;
    display: block;
    border-left: 2px solid transparent;
    margin: 0;
    text-decoration: none !important;
}

.model_content_textarea {
    font-size: 13px;
    position: relative;
}

.post_field_user_link {
    display: block;
    height: 0;
    position: absolute;
    float: none;
    left: 20px;
    top: 12px;
}
.post_field_user_image {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    position: absolute;
}

.model_content_textarea textarea {
    position: relative;
    margin-left: 48px;
    vertical-align: top;
    overflow: hidden;
    width: calc(100% - 40px);
    min-height: 52px;
    max-height: 52px;
    padding: 16px 20px 16px 12px;
    background-color: transparent;
    border: 0;
    line-height: 1.462;
    word-wrap: break-word;
    resize: none;
}

.model_content_textarea.shown textarea {
    min-height: 80px;
    max-height: none;
}

.model_content_textarea:not(.shown) .post-buttons {
    display: block !important;
    overflow: hidden;
    height: 0;
}

.post-horizontal:not(:empty), .post-vertical:not(:empty), .post-had-geo:not(:empty), .post-source:not(:empty), .post-has-poll:not(:empty), .post-has-geo.appended-geo {
    padding: 0 20px 10px;
    margin: 0;
}
.vertical-attachment .audioEntry {
    max-height: 32px;
    min-height: unset;
}
.vertical-attachment .audioEntry .audioEntryWrapper {
    height: unset;
    padding: 6px 10px;
}
.post-buttons .vertical-attachment .vertical-attachment-content {
    padding: 0;
    max-height: 32px;
}
.attachment_note .attachment_note_text, .post-upload, .post-has-poll, .post-has-note, .post-source {
    color: var(--muted-text-color)
}
.vertical-attachment-remove {
    margin: auto 0;
}
.module_body .audioEntry,.attachments_m .audioEntry {
    margin: 0 -8px;
    width: calc(100% + 16px);
}
.audioEntry .audioEntryWrapper {
    padding: 8px;
}
.audioEntry .mini_timer {
    white-space: nowrap;
}

#wallAttachmentMenu {
    display: flex !important;
    opacity: 1;
    background: none;
    box-shadow: none;
    border: 0;
    min-width: unset;
    position: static;
    margin: 0;
    align-items: center;
}

.model_content_textarea #wallAttachmentMenu img {
    width: 20px;
    height: 20px;
}

.model_content_textarea div[style^="float"]:has(.menu_toggler_vkify) {
    float: none;
    flex-direction: row-reverse;
    justify-content: left;
    margin-top: 6px;
}

.post-bottom-buttons {
    float: right;
    display: flex;
    align-items: center;
}
.post_settings {
    opacity: .7;
    margin-right: 15px;
}
.post_settings .common_icon {
    background-position: 0 -60px
}

#wallAttachmentMenu>a:hover {
    background: none;
}

.model_content_textarea:not(.shown) #wallAttachmentMenu {
    top: 0;
    right: 0;
    z-index: 2;
    position: absolute;
    padding: 15px 20px;
}

.model_content_textarea:not(.shown) #wallAttachmentMenu a:not(#__photoAttachment, #__videoAttachment, #__audioAttachment), .model_content_textarea #wallAttachmentMenu .header, .model_content_textarea .menu_toggler_vkify {
    display: none;
}

#wallAttachmentMenu>a {
    padding: 0 4px 0 0;
}

.model_content_textarea.shown .post-bottom-acts {
    background-color: var(--module-header-background-color);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid var(--border-color);
    border-radius: 0 0 var(--module-border-radius) var(--module-border-radius);
}
.post-attach-menu__icon {
    width: 22px;
    height: 22px;
    background: url(/themepack/vkify16/*******/resource/icons/post_icon.png) no-repeat;
    opacity: .7
}
.post-attach-menu a:hover .post-attach-menu__icon, .post_settings:hover, .post_settings[aria-expanded="true"] {
    opacity: 1;
}

.attach_photo .post-attach-menu__icon {
    background-position: 0% -65px
}
.attach_video .post-attach-menu__icon {
    background-position: 0% -87px
}
.attach_audio .post-attach-menu__icon {
    background-position: 0% -109px
}
.attach_document .post-attach-menu__icon {
    background-position: 0% -131px
}
.attach_note .post-attach-menu__icon {
    background-position: 0% -241px
}
.attach_graffiti .post-attach-menu__icon {
    background-position: 0% -175px
}
.attach_poll .post-attach-menu__icon {
    background-position: 00% -197px
}
.attach_geo .post-attach-menu__icon {
    background-position: 0% -153px
}
.attach_source .post-attach-menu__icon {
    background-position: 0% -327px
}

.wall_note_type {
    display: block;
    font-size: 15px;
    color: var(--text-color);
    margin-top: 4px;
    padding: 16px 0 11px;
    font-weight: 500;
    -webkit-font-smoothing: subpixel-antialiased;
    -moz-osx-font-smoothing: auto;
    border-top: 1px solid var(--border-color);
}

.page_block+.comments, .page_block+#standaloneCommentBox {
    margin-top: -15px;
}

.sort_link_icon {
    background: url('data:image/svg+xml;charset=utf-8,%3Csvg%20height%3D%224%22%20viewBox%3D%22944%201152%208%204%22%20width%3D%228%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22m945.2%201152.2c-.3-.2-.7-.2-.9.1s-.2.7.1.9l3.2%202.6c.*******.8%200l3.2-2.6c.3-.2.3-.6.1-.9s-.6-.3-.9-.1l-2.8%202.3z%22%20fill%3D%22%2392a0b1%22%2F%3E%3C%2Fsvg%3E') no-repeat right 7px;
}
.sort_link_icon_desc {
    transform: rotate(180deg);
    background-position: right 1px;
}
.post_replies_header {
    padding: 14px 20px 4px;
    font-size: 12.5px;
}

/* upload photos */
.whiteBox {
    margin-top: 0;
    height: unset;
    background: none;
    border: none;
    width: 600px;
}
.uploadedImageDescription {
    width: 100%;
    display: flex;
}
.uploadedImageDescription span {
    position: static !important;
    color: var(--muted-text-color) !important;
    margin-right: 10px;
    white-space: nowrap;
}
.uploadedImageDescription textarea {
    margin: 0 !important;
    width: 100%;
}
.insertedPhoto {
    background: none;
    border: 0;
    padding: 0 0 10px;
    margin: 0;
    display: flex;
}
.uploadedImage {
    margin-left: 10px;
}

/* posts */
.post {
    line-height: 14px;
    position: relative;
    overflow: unset;
    width: unset;
}
.post-content {
    border: 0;
}
.post:not(.editing)>.post_edit, .reply:not(.editing)>.post_edit {
    display: none;
}
.post .post_header {
    padding: 15px 20px 8px;
    min-height: 50px;
}

.post_image {
    display: block;
    float: left;
}

.post_header_info {
    margin-left: 65px;
    padding-top: 8px;
}
.post_author {
    line-height: 16px;
    padding-right: 20px;
    display: flex;
    align-items: center;
}
.post_author .author {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300px;
    margin-right: 4px;
}
.post_date {
    font-size: 12.5px;
    padding-top: 2px;
}
.post_date, .post_date .post_link, .post_date .promoted_post, .reply_date, .reply_date .reply_link {
    color: var(--muted-text-color)
}

.post .wall_text {
    padding: 0 20px;
}
.wall_post_text {
    line-height: 1.462;
    padding: 3px 0 8px;
    overflow: hidden;
    word-wrap: break-word;
    display: block;
}

.post-nsfw .post-content::after,.deleted_content .content_page_error {
    background: var(--module-background-color--secondary);
    color: var(--muted-text-color-2);
    font-size: 14px;
}

.post-signature, .post .sourceDiv {
    margin: 2px 20px 15px;
}
.authorIcon {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg%20width%3D%228%22%20height%3D%229%22%20viewBox%3D%2220%20203%208%209%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20style%3D%22fill%3A%23AABBCE%3B%22%3E%3Cpath%20d%3D%22M24%20209c3.5%200%204%201%204%202.5%200%20.5%200%20.5-1%20.5h-6c-1%200-1%200-1-.5%200-1.5.5-2.5%204-2.5zm0-1c-1.1%200-2-1.12-2-2.5s.9-2.5%202-2.5%202%201.12%202%202.5-.9%202.5-2%202.5z%22%2F%3E%3C%2Fsvg%3E") no-repeat;
    height: 9px;
    width: 8px;
    margin-top: 3px;
    margin-right: 6px;
}
.authorName, .post .sourceDiv span {
    font-size: inherit;
    line-height: 15px;
    margin: 0;
}

.reply:not(:first-child) .reply_wrap, .post-menu-s {
    border-top: 1px solid var(--border-color);
}
.post-menu-s .comments {
    border-bottom: 1px solid var(--border-color);
}

.post_full_like_wrap {
    padding: 10px 0 11px;
    margin: 8px 20px 0;
    border-top: 1px solid var(--border-color);
    border-radius: 0 0 var(--module-border-radius) var(--module-border-radius);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.post_full_like {
    display: flex;
    align-items: center;
}

.post_like, .post_share {
    cursor: pointer;
    color: var(--post-action-color);
    white-space: nowrap;
    overflow: hidden;
    margin-right: 6px;
    padding: 4px 7px 5px;
    border-radius: var(--button-border-radius);
    display: block;
}

.wall_module .my_like .post_like_count, .wall_module .my_share .post_share_count {
    color: var(--post-action-active-color)
}
.post_like:hover, .post_share:hover {
    background-color: var(--button-background-color--light);
    text-decoration: none;
}
.post_full_like_wrap .post_like:first-child, .post_full_like_wrap .post_share:first-child {
    margin-left: -7px;
}
.post_like_icon, .post_share_icon {
    display: inline-block;
}
.post_like_link, .post_share_link {
    margin: 1px 0 0 6px;
}

.post_like_count, .post_share_count, .likeCnt {
    font-weight: 700;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: var(--post-meta-color);
    font-size: 13px;
    display: inline-block;
    float: none;
    margin-left: 4px;
}
.likeCnt:empty {
    display: none;
}
.post_like_link:empty+.post_like_count, .post_share_link:empty+.post_share_count {
    margin-left: 0;
}

.post_full_like_wrap .reply_link_wrap {
    padding: 5px 7px;
    margin-right: -7px;
    float: right;
}

.post_like_icon, .post_share_icon {
    background: url(/themepack/vkify16/*******/resource/icons/post_icon.png) no-repeat 0 0;
    display: inline-block;
    width: 16px;
    height: 13px;
    margin: 2px 0 1px;
    float: left;
    opacity: 0.4;
}
.heart:not(#liked):hover {
    opacity: 0.4 !important;
}
@keyframes like-icon-bounce {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}
#liked {
    animation: like-icon-bounce 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.post_share_icon {
    background-position: 0 -15px;
    height: 14px;
    margin-bottom: 0;
    margin: 2px 3px 0;
    width: 14px;
}

.expand_button {
    margin: 15px 20px 0;
    width: unset !important;
}

.post-content .attachments .attachment {
    width: 100%;
}

.post.copy_quote {
    border-left: 2px solid var(--quote-border-color);
    padding-left: 12px;
    margin: 5px 0 0;
}
.post.copy_quote .post-avatar {
    width: 40px;
    height: 40px;
}
.post.copy_quote :is(.post_header, .wall_text) {
    padding-inline: 0;
}
.post.copy_quote .post_header {
    min-height: 40px;
    padding: 0;
}
.post.copy_quote .post_header_info {
    margin-left: 52px;
    padding-top: 3px;
}
.post.copy_quote .wall_text {
    padding: 11px 0 0;
}
.post.copy_quote .wall_post_text {
    padding-top: 0;
}

.written {
    background: none;
    padding: 0 0 6px 0;
}

.post-avatar {
    border-radius: 50% !important;
    height: 50px;
    width: 50px;
    object-fit: cover !important;
    vertical-align: top;
}

.post-online {
    font-size: 0;
    padding-top: 6px;
}
.post-online:after {
    border-radius: 100px;
    border: 2px solid var(--module-background-color);
    position: absolute;
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' version='1.2' viewBox='0 0 8 8' width='8' height='8'%3E%3Cstyle%3E.a%7Bfill:%238ac176%7D%3C/style%3E%3Cpath class='a' d='m8 4c0 1.1-0.4 2.1-1.2 2.8-0.7 0.8-1.7 1.2-2.8 1.2-1.1 0-2.1-0.4-2.8-1.2-0.8-0.7-1.2-1.7-1.2-2.8 0-1.1 0.4-2.1 1.2-2.8 0.7-0.8 1.7-1.2 2.8-1.2 1.1 0 2.1 0.4 2.8 1.2 0.8 0.7 1.2 1.7 1.2 2.8z'/%3E%3C/svg%3E");
    margin-left: 14px;
    margin-top: -18px;
}

.post_actions_icon {
    background: url(/themepack/vkify16/*******/resource/icons/post_more.png) no-repeat 50%;
    position: absolute;
    width: 22px;
    height: 24px;
    top: 17px;
    right: 16.5px;
    cursor: pointer;
}
.post_header .post_actions_icon {
    top: 20px;
    right: 18px;
}

.attachment .media {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.attachment .media.media_makima {
    width: calc(100% - 4px);
    height: calc(100% - 4px);
}

.post_edit .post-bottom-acts {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* replies */
.reply {
    padding: 0 20px;
}
.reply_wrap {
    padding: 12px 0;
}
.reply_author {
    font-size: 12.5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.post.reply .page_verified {
    display: inline-block;
    transform: translateY(2px);
}
.reply_image {
    display: block;
    float: left;
}
.reply_content {
    margin-left: 52px;
    word-wrap: break-word;
    min-height: 40px;
}
.reply_img {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    vertical-align: top;
}
.like_icon {
    background: url(/themepack/vkify16/*******/resource/icons/post_icon.png) no-repeat 0 -29px;
    width: 12px;
    height: 10px;
    margin: 2px 0 0;
    opacity: 0.4;
    transition: opacity 100ms ease;
}
.reply_date {
    float: left;
    padding-right: 8px;
}
.reply .like_wrap {
    padding: 10px;
    margin: -10px;
}
.reply .like_wrap:hover {
    text-decoration: none;
}
.reply:hover .like_wrap:hover .like_icon {
    opacity: 0.65;
}
.reply_actions_wrap {
    position: relative;
    z-index: 10
}
.reply_actions {
    height: 11px;
    padding-top: 4px;
    position: absolute;
    right: 0;
    white-space: nowrap;
    display: inline-flex;
}
.reply_action {
    background: url(/themepack/vkify16/*******/resource/icons/post_icon.png) no-repeat 1px -52px;
    opacity: 0;
    filter: alpha(opacity=0);
    cursor: pointer;
    height: 13px;
    width: 13px;
    margin-left: 7px;
    display: inline-block;
}
.reply_action.reply_edit_action {
    background-position: 0 -40px
}
.reply:hover .reply_action {
    visibility: visible;
    opacity: 0.6;
}
.reply .reply_action:hover {
    opacity: 1;
}
.reply_action.reply_report_action {
    background: url(/themepack/vkify16/*******/resource/icons/common.png) 0px -455px;
    background-size: 24px;
}
.reply .reply_action.reply_report_action:hover {
    opacity: .9;
}
.post.reply .reply_text {
    padding: 3px 0 0;
    line-height: 16px;
}
.reply .edit_menu {
    padding: 6px 0 0;
}
.reply_footer {
    font-size: 12.5px;
    padding: 4px 0 0;
    margin-bottom: -1px;
}
.reply_footer .likeCnt {
    margin-left: 3px;
}

/* album photos */
.album-flex {
    column-count: 3;
    column-gap: 10px;
    max-width: 755px;
    margin: 0 auto;
    display: block;
}

.album-photo, .attachment_selector #attachment_insert .photosList .album-photo {
    display: inline-block;
    width: 100%;
    max-height: 250px !important;
    margin-bottom: 10px;
    break-inside: avoid;
    position: relative;
    text-align: center;
}

.album-photo img {
    display: block;
    width: 100%;
    height: auto;
    max-height: 250px !important;
    object-fit: contain;
    padding: 0;
    background: var(--module-background-color--secondary);
    border-radius: 3px;
    border: 0;
    max-width: 100%;
}
.attachment_selector {
    margin: -20px -25px !important;
    padding: 0 15px;
}
.attachment_selector #attachment_insert .photosList {
    margin-top: 35px;
}
.attachment_selector #attachment_insert #attachment_insert_count {
    width: 600px;
    background: var(--module-background-color)
}
.attachment_selector .topGrayBlock {
    margin: 0 -15px;
    padding: 0 15px;
}
.attachment_selector .topGrayBlock {
    background: var(--module-header-background-color);
    border-color: var(--border-color)
}

/* paginator */
.paginator {
    padding: 5px 10px;
    background-color: var(--module-background-color);
    box-shadow: 0 1px 0 0 var(--shadow-bottom-color), 0 0 0 1px var(--shadow-outline-color);
    border-radius: var(--module-border-radius)
}
.paginator a {
    border-top: 3px solid transparent;
    display: inline-block;
    color: var(--link-color);
    padding: 5px;
}
.paginator a:hover {
    border-top: 3px solid rgba(var(--accent-color--rgb), .5);
    background-color: var(--button-background-color--light);
    text-decoration: none;
}
.paginator a.active {
    border-top: 3px solid var(--accent-color);
    font-weight: bold;
    color: var(--text-color)
}

/* page header search */
#searchBoxFastTips {
    background: var(--module-background-color);
    border: 1px solid var(--border-color);
    border-radius: 0 0 var(--module-border-radius) var(--module-border-radius);
    overflow: hidden;
}
.fastavatarlnk {
    display: flex;
    align-items: center;
    padding: 5px 10px;
    border-bottom: 1px solid var(--border-color)
}
.fastavatar {
    height: 35px;
    width: 35px;
    border-radius: 50%;
    margin-right: 10px;
}
.fastavatarlnk span {
    font-weight: 500;
}
#searchBoxFastTips div:not(:last-child) .fastresult {
    border-bottom: 1px solid var(--border-color)
}
#searchBoxFastTips .fastresult {
    padding: 10px;
}
#searchBoxFastTips .fastresult:hover, .fastavatarlnk:hover {
    background: var(--button-background-color--light)
}

/* Search Bar Component Styles */
.ui_filters_block {
    padding: 7px 15px 5px
}
.ui_filters_sibling {
    margin-top: 1px;
    border-radius: 0 0 4px 4px
}
.search_filter_main, .search_filter_open, .search_filter_shut {
    margin: 6px 0 0 0;
    padding: 0 0 13px;
    cursor: pointer;
    font-weight: 700;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: var(--muted-text-color-2);
    font-size: 12.5px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.search_option_content {
    margin-bottom: 15px;
}
.search_option_content label:first-child {
    margin-top: 0;
}
.search_option_content input+input, .search_option_content label select {
    margin-top: 10px;
}

#search_submit {
    display: none;
}

/* Avatar flip animation for post as group functionality */
@keyframes avatar-flip-out {
    0% {
        transform: scaleX(1);
        opacity: 1;
    }
    100% {
        transform: scaleX(0);
        opacity: 0;
    }
}

@keyframes avatar-flip-in {
    0% {
        transform: scaleX(0);
        opacity: 0;
    }
    100% {
        transform: scaleX(1);
        opacity: 1;
    }
}

.post_field_user_image {
    transition: none;
}

.post_field_user_image.avatar-flipping {
    animation: avatar-flip-out 0.1s ease-out forwards;
}

.post_field_user_image.avatar-showing {
    animation: avatar-flip-in 0.1s ease-out forwards;
}

.content_page_error {
    background: var(--module-background-color);
    border: 0;
    text-align: center;
    color: var(--muted-text-color-2);
    padding: 20px 0;
    min-height: 250px;
}
.content_page_error span>b {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
}
.content_page_error span {
    color: var(--muted-text-color-2)
}

/* videos */
.video_items_list {
    display: flex;
    flex-wrap: wrap;
}
.video_block_layout {
    padding: 0 15px 0;
    position: relative;
    z-index: 1;
}
.bsdn {
    aspect-ratio: 16/9;
}
.bsdn_video>video {
    width: 100%;
}
.video_item {
    width: 244px;
    height: 197px;
    padding: 5px;
    margin: 0 1px 18px 0;
    display: inline-block;
    vertical-align: top;
    border-radius: 2px;
    line-height: 13px;
    position: relative;
}
.video_item .video_item_thumb_wrap {
    background-size: initial;
    background-image: url("/web/20200518035514im_/https://vk.com/images/icons/video_empty.png");
    background-color: var(--module-header-background-color);
    background-repeat: no-repeat;
    background-position: 50%;
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}
.video_item .video_item_thumb {
    width: 244px;
    height: 136px;
    cursor: pointer;
    border-radius: 2px;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: 50%;
    -ms-high-contrast-adjust: none;
    position: relative;
    display: block;
}
.video_item_controls {
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 5;
}
.video_thumb_label {
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 5;
    margin: 8px;
    padding: 0 7px;
    border-radius: 3px;
    background: var(--overlay-background);
    color: var(--white);
    font-size: 12.5px;
    line-height: 23px;
    height: 23px;
    white-space: nowrap;
}
.video_thumb_play {
    opacity: 0;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.5) url(/themepack/vkify16/*******/resource/video_play_small.png?1) 19px 13px no-repeat;
    margin: auto;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    transition: background-color 100ms linear, opacity 60ms ease;
}
.video_item:hover .video_thumb_actions, .video_item:hover .video_thumb_play {
    opacity: 1;
}
.video_thumb_play:hover {
    background-color: rgba(0, 0, 0, 0.6);
}
.video_item_info {
    color: var(--muted-text-color);
    padding: 8px 0 0;
    font-size: 12.5px;
}
.video_item_info .video_item_title {
    padding: 2px 0 3px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    white-space: nowrap;
    overflow: hidden;
    display: block;
    text-overflow: ellipsis;
}
.video_item_title span {
    font-weight: 700;
}
.video_item_info .video_item_author {
    padding: 2px 0 3px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
}
.video_item_info .video_item_add_info {
    white-space: nowrap;
    overflow: hidden;
    display: block;
    text-overflow: ellipsis;
    margin-top: 2px;
}
.video_item_info .video_item_add_info {
    white-space: nowrap;
}
.video_item_info .video_item_updated {
    padding: 2px 0 3px;
}
.video_thumb_actions {
    position: absolute;
    top: 0;
    right: 0;
    margin: 8px;
    padding: 3px;
    border-radius: 3px;
    transition: opacity 60ms linear;
    z-index: 5;
    color: var(--white);
    background: var(--overlay-background);
    font-size: 12.5px;
    line-height: 0;
    opacity: 0;
    cursor: default
}
.video_no_actions .video_thumb_actions {
    display: none
}
.video_thumb_actions>div {
    cursor: pointer;
    opacity: .7;
    display: inline-block;
    padding: 3px;
    top: 0
}
.video_thumb_actions>div:hover {
    opacity: 1
}
.video_thumb_actions>div:active {
    position: relative;
    top: 1px
}
.video_thumb_actions .icon {
    width: 16px;
    height: 16px;
    background-repeat: no-repeat;
    background-image: url(/themepack/vkify16/*******/resource/icons/photo_edit_actions.png)
}
.video_thumb_actions .video_thumb_action_edit .icon {
    background-position: 0 -65px
}
.video_thumb_actions .video_thumb_action_delete .icon {
    background-position: 0 -32px
}

/* albums */
.photo_items_list {
    box-sizing: border-box;
    position: relative;
    display: flex;
    flex-wrap: wrap;
    margin: -5px;
}
.photo_items_list .page_album_row {
    width: 245px;
    height: 165px;
    display: inline-block;
    margin: 5px;
    vertical-align: top;
}
.photo_items_list .page_album_nocover {
    height: 165px;
    width: 245px;
}
.photo_items_list .page_album_thumb {
    height: 165px;
    width: 100%;
    object-fit: cover;
}

.page_block:not(.modal) .video_block_layout iframe {
    width: 765px !important;
    height: 430px !important;
}

/* discussion boards */
.controls.blist_last {
    float: right;
    width: 200px;
    padding: 8px 12px;
    border-radius: var(--module-border-radius);
    overflow: hidden;
    cursor: pointer;
}
.controls.blist_last:hover {
    background-color: var(--module-background-color--secondary);
    text-decoration: none;
}
.blist_last .avatar {
    margin-right: 10px;
    width: 40px;
    height: 40px;
    overflow: hidden;
    float: left;
}
.blist_last .avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}
.blist_last .info {
    text-align: start;
    padding-top: 4px;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.blist_last .info .labeled.name {
    margin: 0;
}
.blist_last .info .labeled:not(.name) {
    color: var(--muted-text-color);
    padding-top: 3px;
    overflow: hidden;
}
.bt_header {
    border-bottom: 1px solid var(--border-color)
}
#standaloneCommentBox {
    background: var(--module-background-color);
    border-bottom: 0;
    border-top: 1px solid var(--border-color);
}

.note_post img {
    max-width: 245pt;
    max-height: 200pt;
}
.note_post blockquote {
    word-wrap: break-word;
    padding: 10px;
    background-color: var(--module-background-color--secondary);
    border-left: 3px solid var(--border-color);
    border-bottom: 0;
    margin: 10px 0 0 0;
}

.bsdn-player {
    height: 100%;
}

/* docs */
.docListViewItem:hover, .attachButton:hover {
	background: var(--button-background-color--light);
}
.docListViewItem .doc_volume > div {
	height: 13px;
	width: 13px;
	background: url('/themepack/vkify16/*******/resource/icons/common.png');
}
.docListViewItem .doc_volume > div,.docGalleryItem .doc_top_panel > div {
	opacity: .7;
	transition: .2s;
}
.docListViewItem .doc_volume > div:hover,.docGalleryItem .doc_top_panel > div:hover {
	opacity: 1;
}
.docListViewItem .doc_volume #edit_icon {
	background-position: 0px -276px;
}
.docListViewItem .doc_volume #remove_icon {
	background-position: 1px -28px;
}
.docListViewItem .doc_content {
	justify-content: center;
	gap: 2px;
	margin: 0;
}
.docListViewItem .doc_content b {
	color: var(--text-color)
}
.docListViewItem .doc_content .doc_content_info, .docListViewItem .doc_content span {
	color: var(--muted-text-color);
}
.docGalleryItem {
	display: block;
}
.docGalleryItem .doc_top_panel,.docGalleryItem .doc_bottom_panel {
	background: var(--overlay-background)
}
.docListViewItem .doc_icon {
	background: var(--module-background-color--secondary)
}
.doc_icon.no_image span::before {
	content: '';
	display: inline-block;
	width: 10px;
	height: 10px;
	background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAAKCAYAAABmBXS+AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsIAAA7CARUoSoAAAABYSURBVChTY2RAAvNnzmwBUtVA3JqYnl4DFgQCJigNAyAFyDQYoCvCCohSxAh0x38oGydANwmmAUUjE9AXKD5EByB5uAIka0E0WBxmAHo4wa3BawOmRxgYAOA2GQMa5cgCAAAAAElFTkSuQmCC");
	transform: translateY(2px) translateX(-3px);
	background-repeat: no-repeat;
}
.docListViewItem .doc_icon.no_image span {
	color: var(--post-action-color);
	font-weight: bold;
}

/* language */
.langSelect {
	display: flex;
	align-items: center;
	padding: 6px 0;
}

/* dialogi */
ul.im-page--dcontent {
    padding: 0;
    margin: 0;
}
.nim-peer {
    width: 46px;
    height: 46px;
    position: relative;
    border-color: inherit;
    background-color: inherit;
}
.nim-peer .nim-peer--photo-w {
    overflow: hidden;
    border-radius: 50%;
}
.nim-peer .im_grid {
    display: block;
    float: left;
}
.nim-peer .nim-peer--photo {
    background-color: inherit;
    overflow: hidden;
    margin-left: -2px;
    margin-bottom: -1px;
}
.nim-peer .nim-peer--photo .im_grid>img {
    width: 46px;
    height: 46px;
    border-radius: 50%;
    -moz-force-broken-image-icon: 0;
    background-color: var(--module-background-color);
    position: relative;
    background-color: inherit;
}
.nim-peer img {
    width: 46px;
    height: 46px;
    display: block;
}
.nim-peer .im_grid img {
    margin-left: 2px;
    margin-bottom: 1px;
}
.nim-peer.nim-peer_smaller {
    width: 30px;
    height: 30px;
}
.nim-peer.nim-peer_smaller .nim-peer--photo {
    background-color: inherit;
    overflow: hidden;
    margin-left: -2px;
    margin-bottom: -1px;
}
.nim-dialog {
    height: 63px;
    box-sizing: border-box;
    padding: 0 0 0 15px;
    display: block;
    width: 100%;
    cursor: pointer;
}
.nim-dialog.nim-dialog--unread {
    background: var(--button-background-color)
}
.nim-dialog .nim-dialog--content {
    padding-right: 15px;
    margin-left: 57px;
    position: relative;
    display: block;
    text-decoration: none;
}
.nim-dialog .nim-dialog--photo {
    padding: 9px 7px 7px 0;
    float: left;
}
.nim-dialog .nim-dialog--date {
    opacity: .7;
}
.nim-dialog .nim-dialog--name {
    font-size: 12.5px;
    font-weight: 500;
    -webkit-font-smoothing: subpixel-antialiased;
    -moz-osx-font-smoothing: auto;
    margin-bottom: 7px;
    margin-top: 3px;
    width: 100%;
    height: 18px;
    position: relative;
}
.nim-dialog .nim-dialog--name .nim-dialog--name-w {
    color: var(--vkui--color_text_primary);
    max-width: 70%;
    white-space: nowrap;
    display: inline-block;
    vertical-align: top;
    text-overflow: ellipsis;
    overflow: hidden;
    padding-bottom: 1px;
}
.nim-dialog .nim-dialog--cw {
    padding: 8px 0;
    position: relative;
}
.nim-dialog .nim-dialog--cw .nim-dialog--date_wrapper {
    display: flex;
    position: absolute;
    top: 11px;
    right: 0;
}
.nim-dialog .nim-dialog--who {
    color: var(--muted-text-color)
}
.nim-dialog .nim-dialog--date {
    color: var(--muted-text-color);
    font-size: 12.5px;
}
.nim-dialog:not(.nim-dialog_deleted):hover {
    background: var(--module-background-color--secondary);
}
.nim-dialog:not(.nim-dialog_deleted):hover:last-child {
    border-bottom: solid 1px var(--border-color);
}
.nim-dialog .nim-dialog--mute {
    display: none;
}
.nim-dialog.nim-dialog_muted .nim-dialog--name-w {
    position: relative;
    padding-right: 9px;
}
.nim-dialog.nim-dialog_muted .nim-dialog--name .nim-dialog--name-w {
    max-width: 59%;
}
.nim-dialog .nim-dialog--inner-text:empty {
    background: 0 0 !important;
}
.nim-dialog.nim-dialog_classic {
    height: 71px;
    padding-left: 20px;
}
.nim-dialog.nim-dialog_classic .nim-dialog--text-preview {
    margin-top: -2px;
}
.nim-dialog.nim-dialog_classic.nim-dialog_muted .nim-dialog--name .nim-dialog--name-w {
    max-width: 75%;
}
.nim-dialog.nim-dialog_classic .nim-dialog--cw {
    height: 71px;
    box-sizing: border-box;
    padding: 10px 0;
}
.nim-dialog.nim-dialog_classic .nim-dialog--name {
    height: 19px;
    margin-top: 3px;
    margin-bottom: 7px;
}
.nim-dialog.nim-dialog_classic .nim-dialog--content {
    margin-left: 64px;
}
.nim-dialog.nim-dialog_classic .nim-dialog--photo {
    padding-right: 14px;
    padding-top: 11px;
}
.nim-dialog.nim-dialog_classic .nim-dialog--photo .nim-peer, .nim-dialog.nim-dialog_classic .nim-dialog--photo .nim-peer img {
    width: 50px;
    height: 50px;
}
.nim-dialog.nim-dialog_classic:hover .nim-dialog--date {
    display: block;
}
.nim-dialog.nim-dialog_classic .nim-dialog--date_wrapper {
    right: 15px;
    top: 14px;
}
.nim-dialog+.nim-dialog .nim-dialog--content {
    border-top: solid 1px var(--border-color);
}
.im-prebody {
    display: inline-block;
    margin-right: 4px;
    vertical-align: middle;
}
.im-prebody img {
    position: relative;
    overflow: hidden;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    -moz-force-broken-image-icon: 0;
    background-color: var(--module-header-background-color);
    position: relative;
}
.messenger-app, .messenger-app--header {
    background-color: var(--module-background-color)
}
.messenger-app {
    border-color: var(--border-color);
    border-radius: var(--module-border-radius);
}
.messenger-app--header, .messenger-app-header--actions {
    display: flex;
}
.messenger-app--header {
    border-bottom: 1px solid var(--border-color)
}
.messenger-app--header--back:hover {
    background: linear-gradient(to right, var(--module-background-color--secondary), transparent)
}
.messenger-app--header--back a {
    cursor: pointer;
    display: block;
    height: 19px;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI5IiBoZWlnaHQ9IjE2IiB2aWV3Qm94PSIwIDAgOSAxNiI+PHBhdGggZmlsbD0iIzgyOEE5OSIgZD0iTTggMTUuOWMtLjIgMC0uNC0uMS0uNi0uM2wtNy03Yy0uMy0uMy0uMy0uOSAwLTEuMmw3LTdjLjMtLjMuOS0uMyAxLjIgMCAuMy4zLjMuOSAwIDEuMmwtNi40IDYuNCA2LjQgNi40Yy4zLjMuMy45IDAgMS4yLS4yLjItLjQuMy0uNi4zeiIgb3BhY2l0eT0iLjciLz48L3N2Zz4=');
    background-color: transparent;
    color: var(--muted-text-color);
    text-decoration: none;
    font-size: 14px;
    line-height: 19px;
    background-position: 13px 16px;
    background-repeat: no-repeat;
    padding: 14px 20px 15px 31px;
}
.messenger-app--header--ava img {
    height: 30px;
    width: 30px;
    display: block;
}
.messenger-app--header--info {
    flex: 1;
    text-align: center;
    padding: 7px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: flex;
    align-items: center;
    justify-content: center;
}
.messenger-app--header--name a {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: var(--text-color);
    font-weight: 500;
}
.messenger-app--header--ava {
    margin: 9px 15px 6px 0;
    position: relative;
}
.messenger-app--header--ava .messenger-app--header--online {
    width: 6px;
    height: 6px;
    border-radius: 6px;
    background-color: var(--success-color);
    border: 2px solid var(--module-background-color);
    right: -2px;
    bottom: 2px;
    position: absolute;
}
.messenger-app--header--online {
    margin-left: 4px;
    color: var(--muted-text-color)
}
.messenger-app-header--more-actions--trigger {
    height: 48px;
    width: 48px;
    background: url(/themepack/vkify16/*******/resource/icons/post_more.png) no-repeat center center;
    cursor: pointer;
}

.messenger-app--messages {
    padding: 0;
    height: calc(100vh - var(--header-height) - 15px - 49px - 65px)
}
.messenger-app--messages---message {
    margin-bottom: 0;
    padding: 15px 20px 10px;
}
.messenger-app--messages---message .ava {
    border-radius: 50%;
    height: 40px;
    width: 40px;
}
.messenger-app--messages---message, .messenger-app--input {
    justify-content: unset;
}
.messenger-app--messages---message ._content {
    flex: 1;
    width: 100%;
    padding-left: 15px;
}
.crp-entry--message---text, .messenger-app--messages---message .time {
    color: var(--muted-text-color);
}
.messenger-app--input {
    padding: 12px 20px;
    background-color: var(--module-header-background-color);
    border-top: 1px solid var(--border-color);
    height: unset !important;
    border-radius: 0 0 var(--module-border-radius) var(--module-border-radius);
}
.messenger-app--input .blocked::after {
    margin: 0;
    transform: none;
    position: static;
    font-weight: 500;
}
.messenger-app--input .blocked {
    text-align: center;
    padding: 5px 0;
}
.messenger-app--input>.ava {
    border-radius: 50%;
    height: 40px;
    width: 40px;
}
.messenger-app--input---messagebox {
    width: 100%;
    padding: 0 0 0 15px;
    display: flex;
}
.messenger-app--input---messagebox textarea {
    width: 100%;
    height: 38px !important;
    padding: 9px 10px;
    resize: unset;
    margin-bottom: 0 !important;
    margin-right: 15px;
}
.messenger-app--input---messagebox button {
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iNDkzIDIwIDI0IDI0Ij48ZGVzYz4gQ3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+PGcgZmlsbD0ibm9uZSI+PHJlY3QgeD0iNDkzIiB5PSIyMCIgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0Ii8+PHBhdGggZD0iTTQ5OS4zIDMzLjZDNDk4LjcgMzUuMiA0OTcuOCAzNy42IDQ5Ny41IDM4LjkgNDk3IDQxIDQ5Ni43IDQxLjUgNDk4LjUgNDAuNSA1MDAuMyAzOS41IDUwOSAzNC41IDUxMSAzMy40IDUxMy41IDMxLjkgNTEzLjUgMzIgNTEwLjggMzAuNSA1MDguOCAyOS4zIDUwMC4xIDI0LjQgNDk4LjUgMjMuNSA0OTYuOCAyMi41IDQ5NyAyMyA0OTcuNSAyNS4xIDQ5Ny44IDI2LjQgNDk4LjcgMjguOCA0OTkuMyAzMC4zTDUwNi44IDMyIDQ5OS4zIDMzLjZaIiBmaWxsPSIjODI4QTk5Ii8+PC9nPjwvc3ZnPg==');
    background-repeat: no-repeat;
    background-size: 24px 24px;
    background-position: 50% 50%;
    margin: -12px -20px -12px -15px;
    width: 58px;
    opacity: .7;
    border: 0;
    padding: 0;
    cursor: pointer;
}
.messenger-app--input---messagebox button:hover {
    opacity: 1;
}